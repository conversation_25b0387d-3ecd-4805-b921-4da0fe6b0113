# 📋 Guide d'utilisation - Binou Compta Perso

## 🎯 Premiers pas

### Connexion à l'application

1. Ouvrez votre navigateur sur http://localhost:5173
2. Utilisez les identifiants par défaut :
   - **Utilisateur** : `admin`
   - **Mot de passe** : `password`
3. C<PERSON>z sur "Se connecter"

### Interface principale

Après connexion, vous accédez à une interface moderne avec :
- **Menu de navigation** en haut avec 4 sections
- **Contenu principal** qui change selon la section active
- **Notifications** en bas à droite pour les confirmations

## 📊 Dashboard

Le dashboard est votre page d'accueil. Il affiche :

### Carte de solde
- **Revenus totaux** (en vert)
- **Dépenses totales** (en rouge)  
- **Solde actuel** (vert si positif, rouge si négatif)

### Statistiques
- Nombre total de transactions
- Revenu moyen par transaction
- Dépense moyenne par transaction
- Catégorie la plus utilisée

### Transactions récentes
- Les 5 dernières transactions
- Ave<PERSON> montant, catégorie et date
- Lien vers l'historique complet

## ➕ Ajouter une transaction

### Accès
Cliquez sur "➕ Ajouter Transaction" dans le menu.

### Formulaire
1. **Montant** : Saisissez le montant (ex: 50.99)
2. **Type** : 
   - "Revenu" pour les entrées d'argent
   - "Dépense" pour les sorties d'argent
3. **Catégorie** : Choisissez dans la liste déroulante
4. **Description** : Ajoutez un commentaire (optionnel)
5. Cliquez sur "Ajouter la transaction"

### Après ajout
- Notification de confirmation
- Retour automatique au dashboard
- Mise à jour des statistiques

## 🏷️ Gestion des catégories

### Accès
Cliquez sur "🏷️ Catégories" dans le menu.

### Catégories par défaut
Au premier lancement, cliquez sur "✨ Ajouter les catégories par défaut" pour obtenir 20 catégories courantes :
- 🏠 Logement
- 🚗 Transport
- 🍽️ Alimentation
- 📱 Téléphone
- Et 16 autres...

### Ajouter une catégorie
1. Dans le champ "Nom de la catégorie"
2. Tapez le nom (ex: "🎮 Jeux vidéo")
3. Cliquez sur "✅ Ajouter"

### Supprimer une catégorie
1. Trouvez la catégorie dans la liste
2. Cliquez sur l'icône 🗑️
3. Confirmez la suppression

⚠️ **Attention** : La suppression est définitive !

## 📋 Historique des transactions

### Accès
Cliquez sur "📋 Historique" dans le menu.

### Affichage
- **Tableau complet** de toutes vos transactions
- **Colonnes** : Date, Description, Catégorie, Type, Montant
- **Tri** par date (plus récentes en premier)
- **Badges colorés** : vert pour revenus, rouge pour dépenses

### Actions
- **Supprimer** : Cliquez sur 🗑️ puis confirmez
- **Filtrage** : Utilisez la barre de recherche (si disponible)

## 💡 Conseils d'utilisation

### Bonnes pratiques
1. **Saisissez régulièrement** vos transactions
2. **Utilisez des descriptions claires** pour vous souvenir
3. **Créez des catégories spécifiques** pour un meilleur suivi
4. **Consultez le dashboard** pour suivre votre budget

### Catégories recommandées
- **Fixes** : Loyer, Assurances, Abonnements
- **Variables** : Alimentation, Loisirs, Vêtements
- **Exceptionnelles** : Réparations, Cadeaux, Voyages
- **Revenus** : Salaire, Primes, Autres revenus

### Organisation
- Utilisez des **émojis** pour rendre les catégories visuelles
- Créez des catégories **ni trop générales ni trop spécifiques**
- **Supprimez** les catégories inutilisées

## 🔒 Sécurité

### Déconnexion
- Cliquez sur "🚪 Déconnexion" en haut à droite
- Vos données restent sauvegardées

### Données
- Toutes vos données sont stockées localement
- Aucune information n'est envoyée sur internet
- Base de données SQLite sécurisée

## 🐛 Problèmes courants

### "Erreur de connexion"
- Vérifiez que le serveur est démarré
- Actualisez la page
- Consultez les logs avec `tail -f server.log`

### "Catégorie non trouvée"
- Assurez-vous d'avoir des catégories créées
- Utilisez le bouton "Ajouter catégories par défaut"

### Interface qui ne répond pas
- Actualisez la page (F5)
- Vérifiez la console développeur (F12)
- Redémarrez avec `./restart-all.sh`

## 📱 Utilisation mobile

L'application est optimisée pour mobile :
- **Navigation tactile** intuitive
- **Menu adaptatif** avec icônes
- **Formulaires** optimisés pour le tactile
- **Affichage responsive** sur tous écrans

---

**💡 Besoin d'aide ?** Consultez le [guide développeur](DEVELOPER_GUIDE.md) ou les [scripts de gestion](../SCRIPTS.md).
