import React from 'react'
import BalanceCard from './BalanceCard'
import StatsCard from './StatsCard'

export default function Dashboard({ balance, transactions, lastBackup }) {
  const recentTransactions = transactions.slice(0, 5)


  
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatBackupDate = (dateString) => {
    // La date vient de SQLite CURRENT_TIMESTAMP qui est en UTC
    // Format reçu: "2025-10-02 15:12:44"

    let date

    if (dateString.includes('T') || dateString.includes('Z')) {
      // Format ISO standard
      date = new Date(dateString)
    } else {
      // Format SQLite "YYYY-MM-DD HH:MM:SS" - traiter comme UTC
      const utcDateString = dateString.replace(' ', 'T') + 'Z'
      date = new Date(utcDateString)
    }

    return date.toLocaleString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h2>📊 Tableau de bord</h2>
        <p>Vue d'ensemble de vos finances personnelles</p>
        {lastBackup && (
          <div className="backup-info">
            <p style={{
              fontSize: '0.9rem',
              color: lastBackup.status === 'success' ? '#4caf50' : '#f44336',
              margin: '8px 0 0 0',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              {lastBackup.status === 'success' ? '✅' : '❌'}
              Dernière sauvegarde : {formatBackupDate(lastBackup.backup_date)}
              {lastBackup.status === 'success' && (
                <span style={{ color: '#666', fontSize: '0.8rem' }}>
                  ({lastBackup.transactions_count} transactions, {lastBackup.categories_count} catégories)
                </span>
              )}
            </p>
          </div>
        )}
      </div>

      <BalanceCard balance={balance} />
      <StatsCard transactions={transactions} />

      {recentTransactions.length > 0 && (
        <div className="card">
          <h3>🕒 Transactions récentes</h3>
          <div className="recent-transactions">
            {recentTransactions.map(transaction => (
              <div key={transaction.id} className="recent-transaction-item">
                <div className="transaction-info">
                  <div className="transaction-type">
                    <span className={`badge ${transaction.type === 'in' ? 'income' : 'expense'}`}>
                      {transaction.type === 'in' ? '💰' : '💸'}
                    </span>
                  </div>
                  <div className="transaction-details">
                    <div className="transaction-description">
                      {transaction.description || 'Transaction sans description'}
                    </div>
                    <div className="transaction-category">
                      {transaction.category_name || 'Sans catégorie'} • {formatDate(transaction.date_iso)}
                    </div>
                  </div>
                </div>
                <div className={`transaction-amount ${transaction.type === 'in' ? 'positive' : 'negative'}`}>
                  {transaction.type === 'in' ? '+' : '-'}{formatCurrency(transaction.amount)}
                </div>
              </div>
            ))}
          </div>
          <div className="dashboard-action">
            <p style={{ textAlign: 'center', marginTop: '16px', color: '#666' }}>
              Consultez l'historique complet pour voir toutes vos transactions
            </p>
          </div>
        </div>
      )}

      {transactions.length === 0 && (
        <div className="card empty-state">
          <div className="empty-state-content">
            <div className="empty-state-icon">📝</div>
            <h3>Commencez votre suivi financier</h3>
            <p>Ajoutez votre première transaction pour voir apparaître vos statistiques ici.</p>
          </div>
        </div>
      )}
    </div>
  )
}
