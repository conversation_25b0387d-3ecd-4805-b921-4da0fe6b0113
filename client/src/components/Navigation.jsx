import React from 'react'

export default function Navigation({ currentView, onViewChange, user, onLogout, onShowProfile, onShowAccountSettings }) {
  const menuItems = [
    { id: 'dashboard', label: '📊 Dashboard', icon: '' },
    { id: 'add-transaction', label: '➕ Ajouter Transaction', icon: '' },
    { id: 'categories', label: '🏷️ Catégories', icon: '' },
    { id: 'transactions', label: '📋 Historique', icon: '' }
  ]

  return (
    <nav className="navigation">
      <div className="nav-header">
        <h1>💰 Binou Compta Perso</h1>
        <div className="user-info">
          <button onClick={onShowAccountSettings} className="settings-button" title="Configuration du compte">
            ⚙️
          </button>
          <button onClick={onShowProfile} className="user-profile-button">
            👤 {user}
          </button>
          <button onClick={onLogout} className="logout-button">
            🚪 Déconnexion
          </button>
        </div>
      </div>
      
      <div className="nav-menu">
        {menuItems.map(item => (
          <button
            key={item.id}
            className={`nav-item ${currentView === item.id ? 'active' : ''}`}
            onClick={() => onViewChange(item.id)}
          >
            <span className="nav-icon">{item.icon}</span>
            <span className="nav-label">{item.label}</span>
          </button>
        ))}
      </div>
    </nav>
  )
}
