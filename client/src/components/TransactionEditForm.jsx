import React, { useState, useEffect } from 'react'

export default function TransactionEditForm({ 
  transactionId, 
  categories, 
  onSaved, 
  onCancel, 
  apiBase, 
  authToken 
}) {
  const [amount, setAmount] = useState('')
  const [type, setType] = useState('out')
  const [category, setCategory] = useState('')
  const [description, setDescription] = useState('')
  const [dateIso, setDateIso] = useState('')
  const [loading, setLoading] = useState(false)
  const [loadingTransaction, setLoadingTransaction] = useState(true)
  const [error, setError] = useState('')

  // Charger les données de la transaction à modifier
  useEffect(() => {
    const loadTransaction = async () => {
      try {
        const token = authToken || localStorage.getItem('authToken')
        const response = await fetch(`${apiBase}/transactions/${transactionId}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        })

        if (response.ok) {
          const transaction = await response.json()
          setAmount(transaction.amount.toString())
          setType(transaction.type)
          setCategory(transaction.category_id ? transaction.category_id.toString() : '')
          setDescription(transaction.description || '')
          
          // Convertir la date ISO en format datetime-local pour l'input
          const date = new Date(transaction.date_iso)
          const localDateTime = new Date(date.getTime() - date.getTimezoneOffset() * 60000)
            .toISOString()
            .slice(0, 16)
          setDateIso(localDateTime)
        } else {
          setError('Erreur lors du chargement de la transaction')
        }
      } catch (error) {
        setError('Erreur de connexion au serveur')
      } finally {
        setLoadingTransaction(false)
      }
    }

    if (transactionId) {
      loadTransaction()
    }
  }, [transactionId, apiBase, authToken])

  const submit = async (e) => {
    e.preventDefault()
    setError('')
    setLoading(true)

    const a = parseFloat(amount)
    if (Number.isNaN(a) || a <= 0) {
      setError('Veuillez saisir un montant valide supérieur à 0')
      setLoading(false)
      return
    }

    try {
      const token = authToken || localStorage.getItem('authToken')
      
      // Convertir la date locale en ISO
      const finalDateIso = dateIso ? new Date(dateIso).toISOString() : new Date().toISOString()
      
      const response = await fetch(`${apiBase}/transactions/${transactionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          amount: Math.abs(a),
          type,
          category_id: category || null,
          description: description.trim() || null,
          date_iso: finalDateIso
        })
      })

      if (response.ok) {
        onSaved()
      } else {
        const data = await response.json()
        setError(data.error || 'Erreur lors de la mise à jour')
      }
    } catch (error) {
      setError('Erreur de connexion au serveur')
    } finally {
      setLoading(false)
    }
  }

  if (loadingTransaction) {
    return (
      <div className="card form">
        <h3>✏️ Modifier la transaction</h3>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <div>⏳ Chargement...</div>
        </div>
      </div>
    )
  }

  return (
    <form className="card form" onSubmit={submit}>
      <h3>✏️ Modifier la transaction</h3>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      <div className="row">
        <label htmlFor="edit-amount">💰 Montant *</label>
        <input
          id="edit-amount"
          type="number"
          step="0.01"
          min="0"
          value={amount}
          onChange={e => setAmount(e.target.value)}
          placeholder="ex: 12.50"
          required
          disabled={loading}
        />
      </div>

      <div className="row">
        <label htmlFor="edit-type">📊 Type *</label>
        <select
          id="edit-type"
          value={type}
          onChange={e => setType(e.target.value)}
          disabled={loading}
        >
          <option value="in">💰 Entrée d'argent</option>
          <option value="out">💸 Dépense</option>
        </select>
      </div>

      <div className="row">
        <label htmlFor="edit-category">🏷️ Catégorie</label>
        <select
          id="edit-category"
          value={category}
          onChange={e => setCategory(e.target.value)}
          disabled={loading}
        >
          <option value="">— Aucune catégorie —</option>
          {categories.map(c => (
            <option key={c.id} value={c.id}>{c.name}</option>
          ))}
        </select>
      </div>

      <div className="row">
        <label htmlFor="edit-description">📝 Note (optionnel)</label>
        <input
          id="edit-description"
          type="text"
          value={description}
          onChange={e => setDescription(e.target.value)}
          placeholder="Description de la transaction..."
          maxLength="255"
          disabled={loading}
        />
      </div>

      <div className="row">
        <label htmlFor="edit-date">📅 Date et heure</label>
        <input
          id="edit-date"
          type="datetime-local"
          value={dateIso}
          onChange={e => setDateIso(e.target.value)}
          disabled={loading}
        />
      </div>

      <div className="actions">
        <button
          type="button"
          onClick={onCancel}
          disabled={loading}
          className="secondary"
          style={{ marginRight: '8px' }}
        >
          ❌ Annuler
        </button>
        <button
          type="submit"
          disabled={loading || !amount}
          className="success"
        >
          {loading ? '⏳ Mise à jour...' : '✅ Mettre à jour'}
        </button>
      </div>
    </form>
  )
}
