import React from 'react'

export default function TransactionList({ transactions, onDeleted, onEdit, apiBase, authToken }){
  const del = async (id) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette transaction ?')) return
    try {
      const token = authToken || localStorage.getItem('authToken')
      const response = await fetch(`${apiBase}/transactions/${id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      })
      if (response.ok) {
        onDeleted()
      } else {
        alert('Erreur lors de la suppression')
      }
    } catch (error) {
      alert('Erreur de connexion')
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (!transactions || transactions.length === 0) {
    return (
      <div className="card list">
        <h3>📊 Transactions récentes</h3>
        <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
          <div style={{ fontSize: '3rem', marginBottom: '16px' }}>📝</div>
          <p>Aucune transaction pour le moment</p>
          <p style={{ fontSize: '0.9rem' }}>Ajoutez votre première transaction ci-dessus !</p>
        </div>
      </div>
    )
  }

  return (
    <div className="card list">
      <h3>📊 Transactions récentes ({transactions.length})</h3>
      <div className="table-container">
        <table>
          <thead>
            <tr>
              <th>Date</th>
              <th>Montant</th>
              <th>Type</th>
              <th>Catégorie</th>
              <th>Note</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {transactions.map(t => (
              <tr key={t.id}>
                <td>{formatDate(t.date_iso)}</td>
                <td style={{
                  fontWeight: '600',
                  color: t.type === 'in' ? '#4caf50' : '#f44336'
                }}>
                  {t.type === 'in' ? '+' : '-'}{formatCurrency(t.amount)}
                </td>
                <td>
                  <span className={`badge ${t.type === 'in' ? 'income' : 'expense'}`}>
                    {t.type === 'in' ? '💰 Entrée' : '💸 Dépense'}
                  </span>
                </td>
                <td>{t.category_name || '—'}</td>
                <td style={{
                  maxWidth: '200px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}>
                  {t.description || '—'}
                </td>
                <td>
                  <div style={{ display: 'flex', gap: '4px' }}>
                    <button
                      onClick={() => onEdit && onEdit(t.id)}
                      className="primary"
                      style={{ fontSize: '12px', padding: '4px 8px' }}
                      title="Modifier cette transaction"
                    >
                      ✏️ Modif
                    </button>
                    <button
                      onClick={() => del(t.id)}
                      className="danger"
                      style={{ fontSize: '12px', padding: '4px 8px' }}
                      title="Supprimer cette transaction"
                    >
                      🗑️ Suppr
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}